<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>笔尖传奇写作 - 手机版</title>

    <!-- PWA支持 -->
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#5D9CEC">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="笔尖传奇">

    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iIzVEOUNFQyIvPgo8c3ZnIHg9IjgiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xNC4wNiw5LjAybDAuOTEsMC45MUw1LjY2LDE5LjI0TDQuNzUsMTguMzNMMTQuMDYsOS4wMiBNMTcuNjYsM2MtMC4yNSwwLTAuNTEsMC4xLTAuNywwLjI5bC0xLjgzLDEuODNsMy43NSwzLjc1bDEuODMtMS44M2MwLjM5LTAuMzksMC4zOS0xLjAyLDAtMS40MWwtMi4zNC0yLjM0QzE4LjE3LDMuMDksMTcuOTIsMywyNy42NiwzTDE3LjY2LDN6IE0xMi4wNiw2LjE5TDMsMTUuMjVWMTkuMjRoMy45OWw5LjA2LTkuMDZMMTIuMDYsNi4xOXoiLz4KPC9zdmc+Cjwvc3ZnPgo=">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDE4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iMTgwIiByeD0iNDAiIGZpbGw9IiM1RDlDRUMiLz4KPHN2ZyB4PSI0NSIgeT0iNDUiIHdpZHRoPSI5MCIgaGVpZ2h0PSI5MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xNC4wNiw5LjAybDAuOTEsMC45MUw1LjY2LDE5LjI0TDQuNzUsMTguMzNMMTQuMDYsOS4wMiBNMTcuNjYsM2MtMC4yNSwwLTAuNTEsMC4xLTAuNywwLjI5bC0xLjgzLDEuODNsMy43NSwzLjc1bDEuODMtMS44M2MwLjM5LTAuMzksMC4zOS0xLjAyLDAtMS40MWwtMi4zNC0yLjM0QzE4LjE3LDMuMDksMTcuOTIsMywyNy42NiwzTDE3LjY2LDN6IE0xMi4wNiw2LjE5TDMsMTUuMjVWMTkuMjRoMy45OWw5LjA2LTkuMDZMMTIuMDYsNi4xOXoiLz4KPC9zdmc+Cjwvc3ZnPgo=">
    <style>
        /* --- 移动端专用CSS变量 --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 移动端专用尺寸 */
            --mobile-header-height: 56px;
            --mobile-bottom-nav-height: 60px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-button-height: 48px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
            --mobile-font-size-xl: 24px;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }

        /* --- 基础样式重置 --- */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            height: 100vh;
            overflow-x: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 移动端容器 --- */
        .mobile-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
        }

        /* --- 顶部导航栏 --- */
        .mobile-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .theme-toggle {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: none;
            background: var(--bg-content);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: var(--bg-panel-secondary);
            transform: scale(1.05);
        }

        /* --- 主内容区 --- */
        .main-content {
            flex: 1;
            padding-top: var(--mobile-header-height);
            padding-bottom: var(--mobile-bottom-nav-height);
            overflow-y: auto;
        }

        /* --- 首页内容 --- */
        .home-section {
            padding: var(--mobile-padding);
            min-height: calc(100vh - var(--mobile-header-height) - var(--mobile-bottom-nav-height));
            display: flex;
            flex-direction: column;
        }

        .hero-content {
            text-align: center;
            padding: 40px 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .hero-title {
            font-size: var(--mobile-font-size-xl);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 12px;
            letter-spacing: -0.5px;
        }

        .hero-subtitle {
            font-size: var(--mobile-font-size);
            color: var(--text-light);
            margin-bottom: 40px;
            line-height: 1.5;
        }

        /* --- 版本选择卡片 --- */
        .version-cards {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 32px;
        }

        .version-card {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            padding: 24px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .version-card:active {
            transform: scale(0.98);
        }

        .version-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px var(--shadow-color-heavy);
        }

        .version-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .version-icon {
            font-size: 32px;
            margin-right: 16px;
        }

        .version-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .version-desc {
            color: var(--text-light);
            font-size: var(--mobile-font-size-sm);
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .version-features {
            list-style: none;
            padding: 0;
        }

        .version-features li {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            padding: 4px 0;
            position: relative;
            padding-left: 20px;
        }

        .version-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }

        /* --- 底部导航栏 --- */
        .mobile-bottom-nav {
            height: var(--mobile-bottom-nav-height);
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 100;
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
        }

        .nav-item-text {
            font-size: 12px;
            font-weight: 500;
        }

        /* --- 书架界面 --- */
        .library-section {
            padding: var(--mobile-padding);
            display: none;
        }

        .library-section.active {
            display: block;
        }

        .library-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .library-title {
            font-size: var(--mobile-font-size-xl);
            font-weight: 700;
            color: var(--text-dark);
        }

        .library-actions {
            display: flex;
            gap: 8px;
        }

        .library-btn {
            padding: 8px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .library-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .library-btn.primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 通用内容区域 --- */
        .section-content {
            height: calc(100vh - var(--mobile-header-height) - var(--mobile-bottom-nav-height));
            overflow: hidden;
        }

        .section-content iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .hero-content {
                padding: 24px 0;
            }
            
            .hero-title {
                font-size: 20px;
            }
            
            .version-card {
                padding: 20px;
            }
            
            .version-icon {
                font-size: 28px;
            }
        }

        /* --- 动画效果 --- */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .version-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .version-card:nth-child(2) {
            animation-delay: 0.1s;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航栏 -->
        <header class="mobile-header">
            <h1 class="header-title">笔尖传奇</h1>
            <div class="header-actions">
                <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                    </svg>
                </button>
            </div>
        </header>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 首页内容 -->
            <section id="home-section" class="home-section">
                <div class="hero-content">
                    <h2 class="hero-title">智能写作助手</h2>
                    <p class="hero-subtitle">选择适合您的创作模式，开启智能写作之旅</p>

                    <div class="version-cards">
                        <div class="version-card" onclick="openSimpleVersion()">
                            <div class="version-header">
                                <span class="version-icon">🌟</span>
                                <h3 class="version-title">简约版</h3>
                            </div>
                            <p class="version-desc">为新手用户精心设计，简单三步即可开始创作</p>
                            <ul class="version-features">
                                <li>智能引导配置</li>
                                <li>预设提示词模板</li>
                                <li>一键式操作流程</li>
                                <li>新手友好界面</li>
                            </ul>
                        </div>

                        <div class="version-card" onclick="openProVersion()">
                            <div class="version-header">
                                <span class="version-icon">⚡</span>
                                <h3 class="version-title">专业版</h3>
                            </div>
                            <p class="version-desc">为专业创作者打造，提供完整的创作控制</p>
                            <ul class="version-features">
                                <li>自定义工作流程</li>
                                <li>高级提示词编辑</li>
                                <li>多模型协同创作</li>
                                <li>专业数据管理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>


        </main>

        <!-- 底部导航栏 -->
        <nav class="mobile-bottom-nav">
            <div class="nav-item active">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span class="nav-item-text">首页</span>
            </div>
            <div class="nav-item" onclick="window.location.href='writing.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                </svg>
                <span class="nav-item-text">写作</span>
            </div>
            <div class="nav-item" onclick="window.location.href='chat.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
                <span class="nav-item-text">对话</span>
            </div>
            <div class="nav-item" onclick="window.location.href='creative.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <span class="nav-item-text">创意</span>
            </div>
            <div class="nav-item" onclick="window.location.href='library.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                </svg>
                <span class="nav-item-text">书架</span>
            </div>
            <div class="nav-item" onclick="window.location.href='profile.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                </svg>
                <span class="nav-item-text">我的</span>
            </div>
        </nav>
    </div>

    <script>
        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('mobile-theme', newTheme);
        }

        // 加载保存的主题
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 版本选择功能
        function openSimpleVersion() {
            // 保存模式选择并跳转到写作界面
            localStorage.setItem('writing-mode', 'simple');
            window.location.href = 'writing.html';
        }

        function openProVersion() {
            // 保存模式选择并跳转到写作界面
            localStorage.setItem('writing-mode', 'pro');
            window.location.href = 'writing.html';
        }

        // 底部导航激活状态管理
        function setActiveNav(index) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            registerServiceWorker();

            // 添加触摸反馈
            document.querySelectorAll('.version-card, .nav-item, .theme-toggle').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                element.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        });

        // 注册Service Worker
        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('./sw.js')
                        .then(function(registration) {
                            console.log('ServiceWorker registration successful with scope: ', registration.scope);

                            // 检查更新
                            registration.addEventListener('updatefound', function() {
                                const newWorker = registration.installing;
                                newWorker.addEventListener('statechange', function() {
                                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                        // 有新版本可用
                                        showUpdateNotification();
                                    }
                                });
                            });
                        })
                        .catch(function(err) {
                            console.log('ServiceWorker registration failed: ', err);
                        });
                });
            }
        }

        // 显示更新通知
        function showUpdateNotification() {
            if (confirm('发现新版本，是否立即更新？')) {
                window.location.reload();
            }
        }

        // 防止页面缩放
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function (e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
