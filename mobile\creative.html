<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>创意 - 笔尖传奇</title>
    <style>
        /* --- 移动端创意界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;
            
            /* 创意专用颜色 */
            --prompt-bg-golden: #FFF8E1;
            --prompt-bg-novel: #E8F5E9;
            --prompt-bg-outline: #E3F2FD;
            --prompt-bg-world: #F3E5F5;
            --prompt-bg-character: #FFE0B2;
            --prompt-bg-script: #E0F2F1;
            --prompt-bg-title: #FFEFD5;
            --prompt-bg-intro: #F0E6FF;
            
            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --prompt-bg-golden: #4A4A3A;
            --prompt-bg-novel: #3A4A3A;
            --prompt-bg-outline: #3A3A4A;
            --prompt-bg-world: #4A3A4A;
            --prompt-bg-character: #4A4A3A;
            --prompt-bg-script: #3A4A4A;
            --prompt-bg-title: #4A453A;
            --prompt-bg-intro: #453A4A;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow-x: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 创意容器 --- */
        .creative-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .creative-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: relative;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .creative-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .header-btn.primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 分类标签栏 --- */
        .category-tabs {
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            padding: 0 var(--mobile-padding);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .category-tabs::-webkit-scrollbar {
            display: none;
        }

        .tabs-container {
            display: flex;
            gap: 8px;
            padding: 12px 0;
            min-width: max-content;
        }

        .category-tab {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .category-tab:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .category-tab.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .category-icon {
            font-size: 16px;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: var(--mobile-padding);
        }

        /* --- 提示词网格 --- */
        .prompts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* --- 提示词卡片 --- */
        .prompt-card {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            padding: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .prompt-card:active {
            transform: scale(0.98);
        }

        .prompt-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px var(--shadow-color);
        }

        .prompt-card.golden {
            background: var(--prompt-bg-golden);
        }

        .prompt-card.novel {
            background: var(--prompt-bg-novel);
        }

        .prompt-card.outline {
            background: var(--prompt-bg-outline);
        }

        .prompt-card.world {
            background: var(--prompt-bg-world);
        }

        .prompt-card.character {
            background: var(--prompt-bg-character);
        }

        .prompt-card.script {
            background: var(--prompt-bg-script);
        }

        .prompt-card.title {
            background: var(--prompt-bg-title);
        }

        .prompt-card.intro {
            background: var(--prompt-bg-intro);
        }

        .prompt-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .prompt-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .prompt-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            flex: 1;
        }

        .prompt-badge {
            background: var(--accent-color);
            color: var(--text-on-primary);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }

        .prompt-desc {
            color: var(--text-light);
            font-size: var(--mobile-font-size-sm);
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .prompt-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
        }

        .prompt-meta {
            display: flex;
            gap: 12px;
        }

        .prompt-action {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-action:hover {
            background: var(--primary-color-hover);
            transform: scale(1.05);
        }

        /* --- 空状态 --- */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .empty-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .prompts-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .prompt-card {
                padding: 16px;
            }
            
            .prompt-icon {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="creative-container">
        <!-- 顶部导航栏 -->
        <header class="creative-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="creative-title">创意工坊</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="showMyPrompts()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                    </svg>
                    我的
                </button>
                <button class="header-btn primary" onclick="createPrompt()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    创建
                </button>
            </div>
        </header>

        <!-- 分类标签栏 -->
        <div class="category-tabs">
            <div class="tabs-container">
                <div class="category-tab active" onclick="filterCategory('all')">
                    <span class="category-icon">🌟</span>
                    <span>全部</span>
                </div>
                <div class="category-tab" onclick="filterCategory('golden')">
                    <span class="category-icon">⭐</span>
                    <span>黄金一章</span>
                </div>
                <div class="category-tab" onclick="filterCategory('novel')">
                    <span class="category-icon">📚</span>
                    <span>小说创作</span>
                </div>
                <div class="category-tab" onclick="filterCategory('outline')">
                    <span class="category-icon">📋</span>
                    <span>大纲设计</span>
                </div>
                <div class="category-tab" onclick="filterCategory('world')">
                    <span class="category-icon">🌍</span>
                    <span>世界观</span>
                </div>
                <div class="category-tab" onclick="filterCategory('character')">
                    <span class="category-icon">👤</span>
                    <span>人物设定</span>
                </div>
                <div class="category-tab" onclick="filterCategory('script')">
                    <span class="category-icon">🎬</span>
                    <span>剧本创作</span>
                </div>
                <div class="category-tab" onclick="filterCategory('title')">
                    <span class="category-icon">🏷️</span>
                    <span>标题生成</span>
                </div>
                <div class="category-tab" onclick="filterCategory('intro')">
                    <span class="category-icon">📜</span>
                    <span>简介撰写</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="prompts-grid" id="promptsGrid">
                <!-- 提示词卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">💡</div>
                <div class="empty-title">暂无相关提示词</div>
                <div class="empty-desc">尝试切换其他分类或创建您自己的提示词</div>
            </div>
        </main>
    </div>

    <script>
        // 提示词数据
        const promptsData = {
            golden: [
                {
                    id: 'g1',
                    title: '黄金开篇模板',
                    desc: '经典的小说开篇结构，快速抓住读者注意力',
                    icon: '⭐',
                    likes: 1234,
                    uses: 5678,
                    category: 'golden'
                },
                {
                    id: 'g2',
                    title: '悬念设置大师',
                    desc: '在开篇就埋下引人入胜的悬念',
                    icon: '🔮',
                    likes: 987,
                    uses: 3456,
                    category: 'golden'
                }
            ],
            novel: [
                {
                    id: 'n1',
                    title: '玄幻小说创作',
                    desc: '专业的玄幻小说创作指导，包含修炼体系设计',
                    icon: '⚔️',
                    likes: 2345,
                    uses: 7890,
                    category: 'novel'
                },
                {
                    id: 'n2',
                    title: '都市言情助手',
                    desc: '现代都市背景的言情小说创作模板',
                    icon: '💕',
                    likes: 1876,
                    uses: 4321,
                    category: 'novel'
                },
                {
                    id: 'n3',
                    title: '科幻世界构建',
                    desc: '构建完整的科幻世界观和技术体系',
                    icon: '🚀',
                    likes: 1543,
                    uses: 3210,
                    category: 'novel'
                }
            ],
            outline: [
                {
                    id: 'o1',
                    title: '三幕式结构',
                    desc: '经典的三幕式故事结构设计模板',
                    icon: '📊',
                    likes: 1654,
                    uses: 4567,
                    category: 'outline'
                },
                {
                    id: 'o2',
                    title: '英雄之旅',
                    desc: '基于坎贝尔英雄之旅的故事大纲',
                    icon: '🗡️',
                    likes: 1432,
                    uses: 3890,
                    category: 'outline'
                }
            ],
            world: [
                {
                    id: 'w1',
                    title: '魔法体系设计',
                    desc: '完整的魔法世界观和法则设定',
                    icon: '🔮',
                    likes: 1987,
                    uses: 5432,
                    category: 'world'
                },
                {
                    id: 'w2',
                    title: '未来科技设定',
                    desc: '科幻小说的技术体系和社会结构',
                    icon: '🤖',
                    likes: 1765,
                    uses: 4123,
                    category: 'world'
                }
            ],
            character: [
                {
                    id: 'c1',
                    title: '立体人物塑造',
                    desc: '创造有血有肉的立体角色',
                    icon: '👤',
                    likes: 2109,
                    uses: 6543,
                    category: 'character'
                },
                {
                    id: 'c2',
                    title: '反派角色设计',
                    desc: '设计令人印象深刻的反派角色',
                    icon: '😈',
                    likes: 1654,
                    uses: 3987,
                    category: 'character'
                }
            ],
            script: [
                {
                    id: 's1',
                    title: '电影剧本格式',
                    desc: '标准的电影剧本写作格式和技巧',
                    icon: '🎬',
                    likes: 1432,
                    uses: 2876,
                    category: 'script'
                },
                {
                    id: 's2',
                    title: '对话写作大师',
                    desc: '写出生动自然的角色对话',
                    icon: '💬',
                    likes: 1876,
                    uses: 4321,
                    category: 'script'
                }
            ],
            title: [
                {
                    id: 't1',
                    title: '吸睛标题生成',
                    desc: '创造引人注目的作品标题',
                    icon: '🏷️',
                    likes: 2345,
                    uses: 7654,
                    category: 'title'
                }
            ],
            intro: [
                {
                    id: 'i1',
                    title: '作品简介撰写',
                    desc: '写出吸引读者的作品简介',
                    icon: '📜',
                    likes: 1987,
                    uses: 5432,
                    category: 'intro'
                }
            ]
        };

        let currentCategory = 'all';
        let allPrompts = [];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            initializePrompts();
            renderPrompts();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 初始化提示词数据
        function initializePrompts() {
            allPrompts = [];
            Object.keys(promptsData).forEach(category => {
                allPrompts = allPrompts.concat(promptsData[category]);
            });
        }

        // 返回功能
        function goBack() {
            window.location.href = 'index.html';
        }

        // 分类筛选
        function filterCategory(category) {
            currentCategory = category;

            // 更新标签状态
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.category-tab').classList.add('active');

            renderPrompts();
        }

        // 渲染提示词
        function renderPrompts() {
            const grid = document.getElementById('promptsGrid');
            const emptyState = document.getElementById('emptyState');

            let filteredPrompts = currentCategory === 'all' ? allPrompts :
                                 allPrompts.filter(prompt => prompt.category === currentCategory);

            if (filteredPrompts.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'flex';
                return;
            }

            grid.style.display = 'grid';
            emptyState.style.display = 'none';

            grid.innerHTML = filteredPrompts.map(prompt => `
                <div class="prompt-card ${prompt.category}" onclick="usePrompt('${prompt.id}')">
                    <div class="prompt-header">
                        <span class="prompt-icon">${prompt.icon}</span>
                        <h3 class="prompt-title">${prompt.title}</h3>
                        <span class="prompt-badge">HOT</span>
                    </div>
                    <p class="prompt-desc">${prompt.desc}</p>
                    <div class="prompt-stats">
                        <div class="prompt-meta">
                            <span>👍 ${prompt.likes}</span>
                            <span>🔥 ${prompt.uses}</span>
                        </div>
                        <button class="prompt-action" onclick="event.stopPropagation(); usePrompt('${prompt.id}')">使用</button>
                    </div>
                </div>
            `).join('');
        }

        // 使用提示词
        function usePrompt(promptId) {
            const prompt = allPrompts.find(p => p.id === promptId);
            if (prompt) {
                // 这里可以跳转到写作界面并应用提示词
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'usePrompt',
                        prompt: prompt
                    }, '*');
                } else {
                    alert(`使用提示词: ${prompt.title}`);
                }
            }
        }

        // 显示我的提示词
        function showMyPrompts() {
            alert('我的提示词功能开发中...');
        }

        // 创建提示词
        function createPrompt() {
            alert('创建提示词功能开发中...');
        }
    </script>
</body>
</html>
