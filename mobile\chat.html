<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>对话 - 笔尖传奇</title>
    <style>
        /* --- 移动端对话界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;
            
            /* 对话专用颜色 */
            --message-bg-user: #E3F2FD;
            --message-bg-ai: var(--bg-panel);
            --message-border-radius: 16px;
            
            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-input-height: 80px;
            --mobile-padding: 16px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --message-bg-user: #3A4558;
            --message-bg-ai: var(--bg-panel);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 对话容器 --- */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .chat-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: relative;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .chat-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 消息区域 --- */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: var(--mobile-padding);
            scroll-behavior: smooth;
            background: var(--bg-main);
        }

        .messages-wrapper {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding-bottom: 20px;
        }

        /* --- 消息样式 --- */
        .message {
            display: flex;
            gap: 8px;
            animation: fadeInUp 0.3s ease-out;
            max-width: 85%;
        }

        .message.user {
            flex-direction: row-reverse;
            align-self: flex-end;
        }

        .message.ai {
            align-self: flex-start;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            margin-top: 4px;
        }

        .message.ai .message-avatar {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .message.user .message-avatar {
            background: var(--accent-color);
            color: var(--text-on-primary);
        }

        .message-content {
            flex: 1;
            padding: 12px 16px;
            border-radius: var(--message-border-radius);
            box-shadow: 0 1px 3px var(--shadow-color-light);
            position: relative;
        }

        .message.ai .message-content {
            background: var(--message-bg-ai);
            border-bottom-left-radius: 4px;
        }

        .message.user .message-content {
            background: var(--message-bg-user);
            border-bottom-right-radius: 4px;
        }

        .message-text {
            font-size: var(--mobile-font-size);
            line-height: 1.5;
            color: var(--text-dark);
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .message-time {
            font-size: 11px;
            color: var(--text-light);
            margin-top: 4px;
            opacity: 0.7;
        }

        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            justify-content: flex-end;
        }

        .message.user .message-actions {
            justify-content: flex-start;
        }

        .message-action-btn {
            padding: 4px 8px;
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 11px;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.2s;
        }

        .message-action-btn:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* --- 输入区域 --- */
        .input-section {
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            padding: var(--mobile-padding);
            box-shadow: 0 -2px 8px var(--shadow-color-light);
            min-height: var(--mobile-input-height);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-textarea {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 12px 16px;
            font-size: var(--mobile-font-size);
            font-family: inherit;
            background: var(--bg-content);
            color: var(--text-dark);
            resize: none;
            outline: none;
            transition: border-color 0.2s;
        }

        .input-textarea:focus {
            border-color: var(--primary-color);
        }

        .input-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: var(--primary-color-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--text-light);
            cursor: not-allowed;
            transform: none;
        }

        .voice-btn {
            width: 36px;
            height: 36px;
            border: 1px solid var(--border-color);
            background: var(--bg-content);
            color: var(--text-dark);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .voice-btn:hover {
            background: var(--accent-color);
            color: var(--text-on-primary);
            border-color: var(--accent-color);
        }

        /* --- 空状态 --- */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 40px 20px;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .empty-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
        }

        /* --- 加载状态 --- */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: var(--message-bg-ai);
            border-radius: var(--message-border-radius);
            border-bottom-left-radius: 4px;
            margin-left: 40px;
            max-width: 80px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--text-light);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .message {
                max-width: 90%;
            }
            
            .input-textarea {
                font-size: 16px; /* 防止iOS缩放 */
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 顶部导航栏 -->
        <header class="chat-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="chat-title">AI对话</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="clearChat()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                    </svg>
                    清空
                </button>
                <button class="header-btn" onclick="showSettings()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    设置
                </button>
            </div>
        </header>

        <!-- 消息区域 -->
        <main class="messages-container" id="messagesContainer">
            <div class="messages-wrapper" id="messagesWrapper">
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">💬</div>
                    <div class="empty-title">开始对话</div>
                    <div class="empty-desc">向AI助手提问，获得创作灵感和写作建议</div>
                </div>
            </div>
        </main>

        <!-- 输入区域 -->
        <section class="input-section">
            <div class="input-container">
                <textarea 
                    class="input-textarea" 
                    id="messageInput"
                    placeholder="输入您的问题..."
                    onkeydown="handleInputKeydown(event)"
                    oninput="adjustTextareaHeight()"
                ></textarea>
                <div class="input-actions">
                    <button class="voice-btn" onclick="toggleVoiceInput()" title="语音输入">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                        </svg>
                    </button>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 全局变量
        let messages = [];
        let isLoading = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            loadChatHistory();
            updateEmptyState();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 返回功能
        function goBack() {
            window.location.href = 'index.html';
        }

        // 输入处理
        function handleInputKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function adjustTextareaHeight() {
            const textarea = document.getElementById('messageInput');
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            input.style.height = 'auto';
            
            // 显示AI正在输入
            showTypingIndicator();
            
            // 模拟AI响应
            setTimeout(() => {
                hideTypingIndicator();
                const aiResponse = generateAIResponse(message);
                addMessage('ai', aiResponse);
                saveChatHistory();
            }, 1500 + Math.random() * 1000);
        }

        // 添加消息
        function addMessage(type, text) {
            const message = {
                id: Date.now(),
                type: type,
                text: text,
                time: new Date().toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                })
            };
            
            messages.push(message);
            renderMessage(message);
            updateEmptyState();
            scrollToBottom();
        }

        // 渲染消息
        function renderMessage(message) {
            const messagesWrapper = document.getElementById('messagesWrapper');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.type}`;
            messageElement.innerHTML = `
                <div class="message-avatar">${message.type === 'ai' ? 'AI' : '我'}</div>
                <div class="message-content">
                    <div class="message-text">${message.text}</div>
                    <div class="message-time">${message.time}</div>
                    <div class="message-actions">
                        <button class="message-action-btn" onclick="copyMessage('${message.id}')">复制</button>
                        ${message.type === 'ai' ? '<button class="message-action-btn" onclick="regenerateMessage(\'' + message.id + '\')">重新生成</button>' : ''}
                    </div>
                </div>
            `;
            
            messagesWrapper.appendChild(messageElement);
        }

        // 显示输入指示器
        function showTypingIndicator() {
            isLoading = true;
            const messagesWrapper = document.getElementById('messagesWrapper');
            const typingElement = document.createElement('div');
            typingElement.id = 'typingIndicator';
            typingElement.className = 'typing-indicator';
            typingElement.innerHTML = `
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            
            messagesWrapper.appendChild(typingElement);
            scrollToBottom();
        }

        function hideTypingIndicator() {
            isLoading = false;
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 模拟AI响应
        function generateAIResponse(userMessage) {
            const responses = [
                '这是一个很有趣的问题！让我为您分析一下...',
                '根据您的描述，我建议您可以从以下几个角度来思考：',
                '这个创意很棒！您可以尝试这样展开：',
                '我理解您的想法，这里有一些建议可能对您有帮助：',
                '让我为您提供一些创作思路：'
            ];
            
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            return randomResponse + '\n\n' + generateDetailedResponse(userMessage);
        }

        function generateDetailedResponse(userMessage) {
            // 这里可以根据用户消息的内容生成更具体的回复
            return '基于您的问题，我认为可以从角色发展、情节推进和环境描写三个方面来完善您的创作。每个方面都有其独特的表现手法和技巧。';
        }

        // 消息操作
        function copyMessage(messageId) {
            const message = messages.find(m => m.id == messageId);
            if (message) {
                navigator.clipboard.writeText(message.text).then(() => {
                    showToast('已复制到剪贴板');
                });
            }
        }

        function regenerateMessage(messageId) {
            const messageIndex = messages.findIndex(m => m.id == messageId);
            if (messageIndex > 0) {
                const userMessage = messages[messageIndex - 1].text;
                
                // 移除原AI消息
                messages.splice(messageIndex, 1);
                renderAllMessages();
                
                // 重新生成
                showTypingIndicator();
                setTimeout(() => {
                    hideTypingIndicator();
                    const newResponse = generateAIResponse(userMessage);
                    addMessage('ai', newResponse);
                    saveChatHistory();
                }, 1000);
            }
        }

        // 工具函数
        function updateEmptyState() {
            const emptyState = document.getElementById('emptyState');
            emptyState.style.display = messages.length === 0 ? 'flex' : 'none';
        }

        function scrollToBottom() {
            const container = document.getElementById('messagesContainer');
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 100);
        }

        function renderAllMessages() {
            const messagesWrapper = document.getElementById('messagesWrapper');
            messagesWrapper.innerHTML = '';
            
            if (messages.length === 0) {
                messagesWrapper.innerHTML = `
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">💬</div>
                        <div class="empty-title">开始对话</div>
                        <div class="empty-desc">向AI助手提问，获得创作灵感和写作建议</div>
                    </div>
                `;
            } else {
                messages.forEach(message => renderMessage(message));
            }
            
            updateEmptyState();
        }

        // 数据持久化
        function saveChatHistory() {
            localStorage.setItem('mobile-chat-history', JSON.stringify(messages));
        }

        function loadChatHistory() {
            const saved = localStorage.getItem('mobile-chat-history');
            if (saved) {
                messages = JSON.parse(saved);
                renderAllMessages();
                scrollToBottom();
            }
        }

        // 清空对话
        function clearChat() {
            if (confirm('确定要清空所有对话记录吗？')) {
                messages = [];
                localStorage.removeItem('mobile-chat-history');
                renderAllMessages();
            }
        }

        // 语音输入（占位符）
        function toggleVoiceInput() {
            showToast('语音输入功能开发中...');
        }

        // 设置（占位符）
        function showSettings() {
            showToast('设置功能开发中...');
        }

        // 提示消息
        function showToast(message) {
            // 简单的提示实现
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--text-dark);
                color: var(--text-on-primary);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 2000);
        }
    </script>
</body>
</html>
